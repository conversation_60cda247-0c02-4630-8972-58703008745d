export interface Word {
  id: string;
  word: string;
  translation: string;
  phonetic: string;
  definition: string;
  examples: string[];
  etymology: string;
  rootsAndAffixes?: {
    root: string;
    meaning: string;
    relatedWords: string[];
  }[];
  difficulty: 1 | 2 | 3 | 4 | 5; // 1最简单，5最难
  tags: string[]; // 如：'CET4', 'CET6', 'TOEFL'等
  imageUrl?: string; // 用于图片记忆
}

export interface WordBank {
  id: number;
  name: string;
  description: string;
  words: Word[];
  totalWords: number;
  createdAt: string;
  updatedAt: string;
}

export interface UserProgress {
  userId: string;
  wordId: number;
  status: 'new' | 'learning' | 'reviewing' | 'mastered';
  correctCount: number;
  wrongCount: number;
  lastReviewedAt: string;
  nextReviewAt: string;
} 