.review-container {
  padding: 2rem 0;
}

.review-card {
  padding: 2rem;
  margin-top: 2rem;
}

.word-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.word-text {
  margin: 0;
  color: #1976d2;
}

.example-text {
  margin: 1rem 0;
  padding: 1rem;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.button-group {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
} 
 