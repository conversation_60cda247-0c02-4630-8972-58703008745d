import React, { useState, useEffect } from 'react';
import { 
  Box, 
  TextField,
  Button,
  Typography, 
  Container,
  Paper,
  IconButton,
  Alert,
} from '@mui/material';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import type { Word } from '../types/word';
import { wordBanks } from '../data/wordBanks';

const SpellingPractice: React.FC = () => {
  const [currentWord, setCurrentWord] = useState<Word | null>(null);
  const [userInput, setUserInput] = useState('');
  const [showResult, setShowResult] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const [audio, setAudio] = useState<HTMLAudioElement | null>(null);
  const [streak, setStreak] = useState(0);

  useEffect(() => {
    loadNewWord();
  }, []);

  const loadNewWord = () => {
    const bank = wordBanks[0]; // 使用第一个词库
    const randomIndex = Math.floor(Math.random() * bank.words.length);
    const word = bank.words[randomIndex];
    setCurrentWord(word);
    setUserInput('');
    setShowResult(false);
    
    // 初始化音频
    const audio = new Audio(`https://api.dictionaryapi.dev/media/pronunciations/en/${word.word}.mp3`);
    setAudio(audio);
  };

  const playSound = () => {
    if (audio) {
      audio.play().catch(error => {
        console.error('播放失败:', error);
      });
    }
  };

  const handleSubmit = () => {
    if (!currentWord) return;
    
    const isAnswerCorrect = userInput.toLowerCase().trim() === currentWord.word.toLowerCase();
    setIsCorrect(isAnswerCorrect);
    setShowResult(true);

    if (isAnswerCorrect) {
      setStreak(prev => prev + 1);
    } else {
      setStreak(0);
    }
  };

  const handleNext = () => {
    loadNewWord();
  };

  if (!currentWord) return <div>Loading...</div>;

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        <Box sx={{ mb: 4, textAlign: 'center' }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            拼写练习
            </Typography>
          <Typography variant="subtitle1" sx={{ mb: 1 }}>
            连续正确: {streak}
            </Typography>
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography variant="body1" sx={{ mb: 2 }}>
            请听写以下单词:
            </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <IconButton onClick={playSound} color="primary" size="large">
              <VolumeUpIcon />
            </IconButton>
            <Typography variant="body2" color="text.secondary">
              点击播放发音
            </Typography>
          </Box>
        </Box>

        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            label="请输入单词"
            variant="outlined"
            value={userInput}
            onChange={(e) => setUserInput(e.target.value)}
            disabled={showResult}
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !showResult) {
                handleSubmit();
              }
            }}
          />
        </Box>

        {showResult && (
          <Box sx={{ mb: 3 }}>
            <Alert severity={isCorrect ? "success" : "error"} sx={{ mb: 2 }}>
              {isCorrect ? "正确!" : `错误! 正确答案是: ${currentWord.word}`}
            </Alert>
            {!isCorrect && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="h6">单词详情:</Typography>
                <Typography>音标: {currentWord.phonetic}</Typography>
                <Typography>释义: {currentWord.definition}</Typography>
                <Typography variant="subtitle2" sx={{ mt: 1 }}>例句:</Typography>
                {currentWord.examples.map((example, index) => (
                  <Typography key={index} sx={{ ml: 2, my: 0.5 }}>
                    • {example}
                  </Typography>
                ))}
              </Box>
            )}
          </Box>
        )}

        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
          {!showResult ? (
            <Button variant="contained" color="primary" onClick={handleSubmit}>
              提交
            </Button>
          ) : (
            <Button variant="contained" color="primary" onClick={handleNext}>
              下一个
            </Button>
          )}
          </Box>
      </Paper>
    </Container>
  );
};

export default SpellingPractice; 