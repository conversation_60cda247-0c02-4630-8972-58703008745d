.learn-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 2rem;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.back-button {
  padding: 0.5rem 1rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.back-button:hover {
  background-color: #0056b3;
}

.progress {
  font-size: 1.2rem;
  color: #666;
}

.word-card {
  max-width: 600px;
  margin: 0 auto;
  background-color: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.word-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.word-header h2 {
  font-size: 2.5rem;
  color: #333;
  margin: 0;
}

.speak-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  transition: transform 0.2s ease;
}

.speak-button:hover {
  transform: scale(1.1);
}

.pronunciation {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 2rem;
  font-style: italic;
}

.meaning-section {
  margin-bottom: 2rem;
}

.show-meaning-button {
  width: 100%;
  padding: 1rem;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.show-meaning-button:hover {
  background-color: #e9ecef;
}

.meaning-content {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 5px;
}

.meaning {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 1rem;
}

.example {
  font-size: 1rem;
  color: #666;
  font-style: italic;
}

.navigation-buttons {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.nav-button {
  flex: 1;
  padding: 1rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.nav-button:hover:not(:disabled) {
  background-color: #0056b3;
}

.nav-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .learn-page {
    padding: 1rem;
  }

  .word-header h2 {
    font-size: 2rem;
  }

  .pronunciation {
    font-size: 1rem;
  }

  .meaning {
    font-size: 1.1rem;
  }
} 