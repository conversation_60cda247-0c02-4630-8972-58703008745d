import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Card,
  CardContent,
  Stack,
  Radio,
  RadioGroup,
  FormControlLabel,
  Divider,
  Chip
} from '@mui/material';
import {
  Diamond,
  Check,
  AccessTime,
  CreditCard,
  LocalOffer
} from '@mui/icons-material';

interface MembershipDialogProps {
  open: boolean;
  onClose: () => void;
}

const membershipPlans = [
  {
    id: 'monthly',
    title: '月度会员',
    price: '29.9',
    originalPrice: '39.9',
    period: '每月',
    features: [
      '无限次单词练习',
      '专业发音训练',
      '智能复习提醒',
      '详细学习报告'
    ]
  },
  {
    id: 'quarterly',
    title: '季度会员',
    price: '79.9',
    originalPrice: '119.7',
    period: '每季度',
    features: [
      '月度会员所有功能',
      '词根词缀讲解',
      '专属学习规划',
      '一对一答疑'
    ],
    popular: true
  },
  {
    id: 'yearly',
    title: '年度会员',
    price: '299.9',
    originalPrice: '478.8',
    period: '每年',
    features: [
      '季度会员所有功能',
      'AI助教服务',
      '学习资料下载',
      '终身技术支持'
    ]
  }
];

export const MembershipDialog = ({ open, onClose }: MembershipDialogProps) => {
  const [selectedPlan, setSelectedPlan] = useState('quarterly');

  const handlePurchase = () => {
    // TODO: 实现支付逻辑
    console.log('Purchasing plan:', selectedPlan);
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          bgcolor: '#1f2937',
          color: 'white'
        }
      }}
    >
      <DialogTitle sx={{ 
        borderBottom: '1px solid rgba(255,255,255,0.1)',
        display: 'flex',
        alignItems: 'center',
        gap: 1
      }}>
        <Diamond sx={{ color: '#60a5fa' }} />
        <Typography variant="h6">选择会员方案</Typography>
      </DialogTitle>
      <DialogContent>
        <RadioGroup
          value={selectedPlan}
          onChange={(e) => setSelectedPlan(e.target.value)}
          sx={{ mt: 2 }}
        >
          <Stack spacing={2}>
            {membershipPlans.map((plan) => (
              <Card 
                key={plan.id}
                sx={{ 
                  bgcolor: selectedPlan === plan.id ? 'rgba(96, 165, 250, 0.1)' : '#1a1b26',
                  border: selectedPlan === plan.id ? '2px solid #60a5fa' : '2px solid transparent',
                  transition: 'all 0.3s',
                  position: 'relative',
                  overflow: 'visible'
                }}
              >
                {plan.popular && (
                  <Chip
                    label="最受欢迎"
                    size="small"
                    sx={{
                      position: 'absolute',
                      top: -12,
                      right: 16,
                      bgcolor: '#60a5fa',
                      color: 'white'
                    }}
                  />
                )}
                <FormControlLabel
                  value={plan.id}
                  control={
                    <Radio 
                      sx={{
                        color: 'rgba(255,255,255,0.7)',
                        '&.Mui-checked': {
                          color: '#60a5fa'
                        }
                      }}
                    />
                  }
                  label=""
                  sx={{ 
                    m: 0,
                    width: '100%',
                    '& .MuiFormControlLabel-label': { flex: 1 }
                  }}
                />
                <CardContent>
                  <Stack direction="row" alignItems="center" spacing={2}>
                    <Box flex={1}>
                      <Typography variant="h6" gutterBottom>
                        {plan.title}
                      </Typography>
                      <Stack direction="row" alignItems="baseline" spacing={1}>
                        <Typography variant="h4" color="#60a5fa">
                          ¥{plan.price}
                        </Typography>
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            textDecoration: 'line-through',
                            color: 'rgba(255,255,255,0.5)'
                          }}
                        >
                          ¥{plan.originalPrice}
                        </Typography>
                        <Typography variant="body2" color="rgba(255,255,255,0.7)">
                          /{plan.period}
                        </Typography>
                      </Stack>
                    </Box>
                    <Divider orientation="vertical" flexItem sx={{ bgcolor: 'rgba(255,255,255,0.1)' }} />
                    <Box flex={2}>
                      <Stack spacing={1}>
                        {plan.features.map((feature, index) => (
                          <Stack 
                            key={index} 
                            direction="row" 
                            spacing={1}
                            alignItems="center"
                          >
                            <Check sx={{ color: '#60a5fa', fontSize: 20 }} />
                            <Typography variant="body2">
                              {feature}
                            </Typography>
                          </Stack>
                        ))}
                      </Stack>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            ))}
          </Stack>
        </RadioGroup>
      </DialogContent>
      <DialogActions 
        sx={{ 
          px: 3, 
          pb: 3,
          borderTop: '1px solid rgba(255,255,255,0.1)',
          pt: 2
        }}
      >
        <Stack 
          direction="row" 
          spacing={2} 
          alignItems="center" 
          sx={{ 
            flex: 1,
            justifyContent: 'space-between'
          }}
        >
          <Stack direction="row" spacing={2}>
            <Stack direction="row" spacing={0.5} alignItems="center">
              <CreditCard sx={{ color: 'rgba(255,255,255,0.7)', fontSize: 20 }} />
              <Typography variant="body2" color="rgba(255,255,255,0.7)">
                支持支付宝/微信支付
              </Typography>
            </Stack>
            <Stack direction="row" spacing={0.5} alignItems="center">
              <LocalOffer sx={{ color: 'rgba(255,255,255,0.7)', fontSize: 20 }} />
              <Typography variant="body2" color="rgba(255,255,255,0.7)">
                可开具发票
              </Typography>
            </Stack>
          </Stack>
          <Stack direction="row" spacing={2}>
            <Button onClick={onClose} sx={{ color: 'rgba(255,255,255,0.7)' }}>
              取消
            </Button>
            <Button
              variant="contained"
              onClick={handlePurchase}
              startIcon={<Diamond />}
              sx={{
                bgcolor: '#60a5fa',
                '&:hover': { bgcolor: '#3b82f6' }
              }}
            >
              立即开通
            </Button>
          </Stack>
        </Stack>
      </DialogActions>
    </Dialog>
  );
}; 