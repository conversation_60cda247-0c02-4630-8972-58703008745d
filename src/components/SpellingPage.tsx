import React, { useState, useEffect, useRef } from 'react';
import {
  Container,
  Box,
  Typography,
  TextField,
  Button,
  IconButton,
  CircularProgress,
  Stack,
  keyframes,
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import { getVocabularyPack, getNextWord } from '../data/vocabularyPacks';
import type { Word } from '../types/word';
import { generateExampleSentence } from '../utils/deepseek';

// 音效
const keyPressSound = new Audio('/sounds/key-press.mp3');
const keyDeleteSound = new Audio('/sounds/key-delete.mp3');
const correctSound = new Audio('/sounds/correct.mp3');
const wrongSound = new Audio('/sounds/wrong.mp3');

// 闪烁动画
const flashAnimation = keyframes`
  0% { opacity: 0.3; }
  50% { opacity: 0.8; }
  100% { opacity: 0.3; }
`;

// 预加载音效
const preloadSounds = () => {
  keyPressSound.load();
  keyDeleteSound.load();
  correctSound.load();
  wrongSound.load();
};

interface GeneratedExample {
  english: string;
  chinese: string;
}

const SpellingPage: React.FC = () => {
  const navigate = useNavigate();
  const { packId } = useParams<{ packId?: string }>();
  const [currentWord, setCurrentWord] = useState<Word | null>(null);
  const [userInput, setUserInput] = useState('');
  const [showResult, setShowResult] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [isFlashing, setIsFlashing] = useState(false);
  const [streak, setStreak] = useState(0);
  const [currentWordId, setCurrentWordId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [startTime] = useState<number>(Date.now());
  const [stats, setStats] = useState({
    time: '00:00',
    inputCount: 0,
    wpm: 0,
    correctCount: 0,
    accuracy: 0,
  });
  const inputRef = useRef<HTMLInputElement>(null);
  const timerRef = useRef<number | null>(null);
  const [generatedExample, setGeneratedExample] = useState<GeneratedExample | null>(null);
  const [isGeneratingExample, setIsGeneratingExample] = useState(false);

  // 预加载音效
  useEffect(() => {
    preloadSounds();
  }, []);

  // 更新计时器
  useEffect(() => {
    const updateTimer = () => {
      const now = Date.now();
      const diff = Math.floor((now - startTime) / 1000); // 秒数
      const minutes = Math.floor(diff / 60);
      const seconds = diff % 60;
      setStats(prev => ({
        ...prev,
        time: `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`,
      }));
    };

    timerRef.current = window.setInterval(updateTimer, 1000);
    return () => {
      if (timerRef.current) {
        window.clearInterval(timerRef.current);
      }
    };
  }, [startTime]);

  // 从词汇包获取单词
  useEffect(() => {
    const loadWord = async () => {
      try {
        setIsLoading(true);
        
        // 优先使用 URL 参数中的 packId
        const selectedPackId = packId || localStorage.getItem('selectedVocabularyPack');
        console.log('Selected pack ID:', selectedPackId);
        
        if (!selectedPackId) {
          console.log('No vocabulary pack selected, redirecting to vocabulary-packs');
          navigate('/vocabulary-packs');
          return;
        }

        // 如果有 URL 参数，更新 localStorage
        if (packId) {
          localStorage.setItem('selectedVocabularyPack', packId);
          // 清除之前的进度
          localStorage.removeItem('currentWordId');
        }

        const pack = getVocabularyPack(selectedPackId);
        console.log('Found pack:', pack?.title);
        
        if (!pack) {
          console.log('Invalid vocabulary pack, redirecting to vocabulary-packs');
          navigate('/vocabulary-packs');
          return;
        }

        if (!pack.words || pack.words.length === 0) {
          console.log('Pack has no words, redirecting to vocabulary-packs');
          navigate('/vocabulary-packs');
          return;
        }

        // 获取当前单词ID
        const storedWordId = localStorage.getItem('currentWordId');
        const word = getNextWord(selectedPackId, storedWordId || null);
        console.log('Next word:', word?.word);
        
        if (word) {
          setCurrentWord(word);
          setCurrentWordId(word.id);
          localStorage.setItem('currentWordId', word.id);
          // 自动播放单词发音
          playPronunciation(word);
        } else {
          console.log('No more words available, redirecting to vocabulary-packs');
          navigate('/vocabulary-packs');
        }
      } catch (error) {
        console.error('Error loading word:', error);
        navigate('/vocabulary-packs');
      } finally {
        setIsLoading(false);
      }
    };

    loadWord();
  }, [navigate, packId]);

  // 播放单词发音
  const playPronunciation = (word: Word) => {
    const speech = new SpeechSynthesisUtterance(word.word);
    speech.lang = 'en-US';
    window.speechSynthesis.speak(speech);
  };

  // 播放按键音效
  const playKeySound = (isDelete: boolean = false) => {
    try {
      const sound = isDelete ? keyDeleteSound : keyPressSound;
      sound.currentTime = 0; // 重置音频播放位置
      sound.volume = 0.5; // 设置音量为 50%
      sound.play();
    } catch (error) {
      console.error('Error playing sound:', error);
    }
  };

  // 处理用户输入
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toLowerCase();
    
    // 限制输入长度不超过当前单词长度
    if (currentWord && value.length <= currentWord.word.length) {
      // 判断是否是删除操作
      const isDelete = value.length < userInput.length;
      
      setUserInput(value);
      setStats(prev => ({
        ...prev,
        inputCount: prev.inputCount + 1,
      }));

      // 播放音效
      playKeySound(isDelete);
    }
  };

  // 检查拼写
  const handleSubmit = () => {
    if (!currentWord) return;
    
    const isSpellingCorrect = userInput.toLowerCase() === currentWord.word.toLowerCase();
    
    if (isSpellingCorrect) {
      correctSound.play();
      setIsCorrect(true);
      setShowResult(true);
      setStreak(prev => prev + 1);
      setStats(prev => ({
        ...prev,
        correctCount: prev.correctCount + 1,
        accuracy: Math.round((prev.correctCount + 1) / (prev.inputCount + 1) * 100),
      }));

      // 更新学习进度
      try {
        const storedProgress = localStorage.getItem('studyProgress');
        if (storedProgress) {
          const progress = JSON.parse(storedProgress);
          const today = new Date();
          progress.wordsLearned += 1;
          progress.todayLearned += 1;
          progress.lastStudyDate = today.toISOString();
          localStorage.setItem('studyProgress', JSON.stringify(progress));
        }
      } catch (error) {
        console.error('Error updating progress:', error);
      }
    } else {
      wrongSound.play();
      // 错误处理
      setAttempts(prev => prev + 1);
      setIsFlashing(true);
      setUserInput('');
      
      // 1秒后停止闪烁
      setTimeout(() => {
        setIsFlashing(false);
      }, 1000);

      // 如果达到3次尝试，显示正确答案
      if (attempts >= 2) {
        setShowResult(true);
        setIsCorrect(false);
        setStreak(0);
        setStats(prev => ({
          ...prev,
          accuracy: Math.round(prev.correctCount / (prev.inputCount + 1) * 100),
        }));
      }
    }
  };

  // 下一个单词
  const handleNextWord = () => {
    setUserInput('');
    setShowResult(false);
    setAttempts(0);
    setIsFlashing(false);
    const selectedPackId = localStorage.getItem('selectedVocabularyPack');
    if (selectedPackId) {
      const nextWord = getNextWord(selectedPackId, currentWordId);
      if (nextWord) {
        setCurrentWord(nextWord);
        setCurrentWordId(nextWord.id);
        playPronunciation(nextWord);
      } else {
        navigate('/vocabulary-packs');
      }
    }
  };

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // 如果已经达到最大长度，阻止除了回车和退格键之外的所有输入
    if (currentWord && userInput.length >= currentWord.word.length && 
        e.key !== 'Enter' && e.key !== 'Backspace') {
      e.preventDefault();
      return;
    }

    // 处理回车键
    if (e.key === 'Enter' && !showResult) {
      handleSubmit();
      return;
    }

    // 处理退格键
    if (e.key === 'Backspace') {
      playKeySound(true);
    }
  };

  // 生成例句
  const generateExample = async (word: string) => {
    setIsGeneratingExample(true);
    try {
      const example = await generateExampleSentence(word);
      setGeneratedExample(example);
    } catch (error) {
      console.error('Error generating example:', error);
    } finally {
      setIsGeneratingExample(false);
    }
  };

  // 在单词加载时预加载例句
  useEffect(() => {
    if (currentWord) {
      generateExample(currentWord.word);
    }
  }, [currentWord]);

  // 高亮显示例句中的单词
  const highlightWord = (sentence: string, word: string) => {
    const regex = new RegExp(`(${word})`, 'gi');
    const parts = sentence.split(regex);
    return (
      <Typography 
        variant="body1" 
        sx={{ 
          color: 'text.primary',
          opacity: 0.9,
          fontStyle: 'italic',
          lineHeight: 1.8
        }}
      >
        {parts.map((part, index) => 
          part.toLowerCase() === word.toLowerCase() ? (
            <Box
              key={index}
              component="span"
              sx={{
                borderBottom: '2px solid',
                borderColor: 'text.primary',
                display: 'inline-block',
                lineHeight: 1
              }}
            >
              {part}
            </Box>
          ) : part
        )}
      </Typography>
    );
  };

  if (isLoading) {
    return (
      <Box sx={{ 
        minHeight: '100vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        bgcolor: 'background.default'
      }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ 
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      bgcolor: 'background.default',
      color: 'text.primary',
    }}>
      {/* 顶部导航栏 */}
      <Box sx={{ 
        p: 2, 
        borderBottom: '1px solid',
        borderColor: 'divider',
        display: 'flex',
        alignItems: 'center',
        gap: 2
      }}>
        <Typography variant="body1" sx={{ opacity: 0.7 }}>
          考研
        </Typography>
        <Typography variant="body1" sx={{ opacity: 0.7 }}>
          第 {currentWordId} 章
        </Typography>
      </Box>

      {/* 主要内容区域 */}
      <Box sx={{ 
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        py: 4
      }}>
        {currentWord && (
          <Container maxWidth="sm" sx={{ textAlign: 'center' }}>
            {/* 单词提示区域 */}
            <Box sx={{ mb: 6 }}>
              {!showResult && (
                <>
                  {/* 虚线和输入框容器 */}
                  <Box sx={{ 
                    position: 'relative',
                    height: 60,
                    mb: 3,
                    width: '100%',
                    maxWidth: `${currentWord.word.length * 2}rem`,
                    mx: 'auto'
                  }}>
                    {/* 虚线容器 */}
                    <Box sx={{ 
                      position: 'absolute',
                      top: '50%',
                      left: 0,
                      transform: 'translateY(-50%)',
                      display: 'flex',
                      gap: '0.5rem',
                      width: '100%',
                      justifyContent: 'flex-start'
                    }}>
                      {Array(currentWord.word.length).fill('-').map((_, index) => (
                        <Typography 
                          key={index} 
                          variant="h4" 
                          component="span"
                          sx={{ 
                            color: isFlashing ? 'error.main' : 'text.primary', 
                            opacity: 0.3,
                            fontFamily: 'monospace',
                            visibility: index < userInput.length ? 'hidden' : 'visible',
                            width: '1.5rem',
                            textAlign: 'center',
                            animation: isFlashing ? `${flashAnimation} 0.5s ease-in-out infinite` : 'none',
                          }}
                        >
                          _
                        </Typography>
                      ))}
                    </Box>

                    {/* 输入的字母 */}
                    <Box sx={{ 
                      position: 'absolute',
                      top: '50%',
                      left: 0,
                      transform: 'translateY(-50%)',
                      display: 'flex',
                      gap: '0.5rem',
                      width: '100%',
                      justifyContent: 'flex-start'
                    }}>
                      {userInput.split('').map((char, index) => (
                        <Typography 
                          key={index} 
                          variant="h4" 
                          component="span"
                          sx={{ 
                            color: 'text.primary',
                            fontFamily: 'monospace',
                            width: '1.5rem',
                            textAlign: 'center'
                          }}
                        >
                          {char}
                        </Typography>
                      ))}
                    </Box>

                    {/* 隐藏的输入框 */}
                    <TextField
                      value={userInput}
                      onChange={handleInputChange}
                      onKeyDown={handleKeyPress}
                      variant="standard"
                      autoFocus
                      inputRef={inputRef}
                      inputProps={{
                        maxLength: currentWord?.word.length,
                      }}
                      sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        opacity: 0,
                        '& .MuiInputBase-root': {
                          height: '100%',
                          fontSize: '2rem',
                          fontFamily: 'monospace',
                          color: 'transparent',
                          caretColor: 'transparent',
                        },
                        '& .MuiInput-underline:before': {
                          borderBottom: 'none'
                        },
                        '& .MuiInput-underline:after': {
                          borderBottom: 'none'
                        },
                        '& .MuiInput-underline:hover:not(.Mui-disabled):before': {
                          borderBottom: 'none'
                        }
                      }}
                      InputProps={{
                        disableUnderline: true,
                      }}
                    />
                  </Box>

                  {/* 尝试次数提示 */}
                  {attempts > 0 && !showResult && (
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        color: '#ff4444',
                        mb: 2,
                        opacity: 0.8
                      }}
                    >
                      还剩 {3 - attempts} 次机会
                    </Typography>
                  )}

                  {/* 发音按钮 */}
                  <IconButton
                    onClick={() => playPronunciation(currentWord)}
                    sx={{
                      color: 'text.primary',
                      opacity: 0.7,
                      '&:hover': {
                        opacity: 1
                      }
                    }}
                  >
                    <VolumeUpIcon />
                  </IconButton>

                  {/* 音标 */}
                  <Typography 
                    variant="body1" 
                    sx={{ 
                      color: 'text.primary',
                      opacity: 0.7,
                      mt: 2 
                    }}
                  >
                    {currentWord.phonetic}
                  </Typography>
                </>
              )}
            </Box>

            {/* 中文释义 */}
            <Typography 
              variant="h6" 
              sx={{ 
                color: 'text.primary',
                opacity: 0.7,
                mb: 4
              }}
            >
              {currentWord.translation}
            </Typography>

            {/* 结果显示 */}
            {showResult && (
              <Box sx={{ textAlign: 'center' }}>
                <Typography
                  variant="h4"
                  color={isCorrect ? 'success.main' : 'error.main'}
                  gutterBottom
                >
                  {isCorrect ? '正确!' : '错误!'}
                </Typography>
                {!isCorrect && (
                  <>
                    <Typography 
                      variant="h5" 
                      sx={{ 
                        color: 'text.primary',
                        opacity: 0.9,
                        mb: 2
                      }}
                    >
                      正确答案: {currentWord.word}
                    </Typography>
                    {/* 显示词性 */}
                    <Typography 
                      variant="body1" 
                      sx={{ 
                        color: 'text.primary',
                        opacity: 0.7,
                        mb: 2
                      }}
                    >
                      词性: {currentWord.tags.find(tag => ['noun', 'verb', 'adjective', 'adverb', 'preposition', 'conjunction', 'pronoun', 'interjection'].includes(tag.toLowerCase()))}
                    </Typography>
                    {/* 显示例句 */}
                    <Box sx={{ mt: 3, mb: 3 }}>
                      <Typography 
                        variant="body1" 
                        sx={{ 
                          color: 'text.primary',
                          opacity: 0.7,
                          mb: 1
                        }}
                      >
                        例句:
                      </Typography>
                      {isGeneratingExample ? (
                        <CircularProgress size={20} sx={{ color: 'text.primary', opacity: 0.7 }} />
                      ) : generatedExample ? (
                        <Box>
                          {/* 英文例句 */}
                          {highlightWord(generatedExample.english, currentWord.word)}
                          {/* 中文翻译 */}
                          <Typography 
                            variant="body1" 
                            sx={{ 
                              color: 'text.primary',
                              opacity: 0.7,
                              mt: 1,
                              fontSize: '0.9em'
                            }}
                          >
                            {generatedExample.chinese}
                          </Typography>
                        </Box>
                      ) : (
                        <Box>
                          {/* 预设的英文例句 */}
                          {highlightWord(currentWord.examples[0], currentWord.word)}
                          {/* 预设的中文翻译（如果有） */}
                          {currentWord.examples[1] && (
                            <Typography 
                              variant="body1" 
                              sx={{ 
                                color: 'text.primary',
                                opacity: 0.7,
                                mt: 1,
                                fontSize: '0.9em'
                              }}
                            >
                              {currentWord.examples[1]}
                            </Typography>
                          )}
                        </Box>
                      )}
                    </Box>
                  </>
                )}
                <Button
                  variant="outlined"
                  onClick={handleNextWord}
                  sx={{
                    color: 'text.primary',
                    borderColor: 'divider',
                    '&:hover': {
                      borderColor: 'text.primary',
                      bgcolor: 'action.hover',
                    },
                    mt: 3
                  }}
                >
                  下一个
                </Button>
              </Box>
            )}
          </Container>
        )}
      </Box>

      {/* 底部统计栏 */}
      <Box sx={{ 
        borderTop: '1px solid',
        borderColor: 'divider',
        p: 2,
        display: 'flex',
        justifyContent: 'space-around',
        bgcolor: 'background.paper'
      }}>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            时间
          </Typography>
          <Typography variant="body1" sx={{ color: 'text.primary' }}>
            {stats.time}
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            输入数
          </Typography>
          <Typography variant="body1" sx={{ color: 'text.primary' }}>
            {stats.inputCount}
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            WPM
          </Typography>
          <Typography variant="body1" sx={{ color: 'text.primary' }}>
            {stats.wpm}
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            正确数
          </Typography>
          <Typography variant="body1" sx={{ color: 'text.primary' }}>
            {stats.correctCount}
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            正确率
          </Typography>
          <Typography variant="body1" sx={{ color: 'text.primary' }}>
            {stats.accuracy}%
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default SpellingPage; 