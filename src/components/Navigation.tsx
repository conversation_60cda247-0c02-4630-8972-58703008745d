import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  App<PERSON><PERSON>,
  Tool<PERSON>,
  Button,
  Box,
  Container,
  Typography,
  IconButton,
  Menu,
  MenuItem,
  useTheme as useMuiTheme,
  useMediaQuery,
  Stack,
  Drawer,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  Divider,
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import PersonOutlineIcon from '@mui/icons-material/PersonOutline';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';
import { LoginDialog } from './LoginDialog';
import { useTheme } from '../contexts/ThemeContext';

const Navigation: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const muiTheme = useMuiTheme();
  const { isDarkMode, toggleTheme } = useTheme();
  const isMobile = useMediaQuery(muiTheme.breakpoints.down('sm'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const [loginOpen, setLoginOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const menuItems = [
    { title: '首页', path: '/' },
    { title: '词汇包', path: '/vocabulary-packs' },
    { title: '统计', path: '/stats' },
  ];

  const handleNavigate = (path: string) => {
    navigate(path);
    setMobileOpen(false);
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleLoginClick = () => {
    setLoginOpen(true);
  };

  const handleLoginClose = () => {
    setLoginOpen(false);
  };

  const handleRegister = () => {
    // 处理注册逻辑
    console.log('Register clicked');
  };

  return (
    <>
      <AppBar 
        position="fixed" 
        sx={{ 
          backgroundColor: isDarkMode ? 'rgba(18, 18, 18, 0.95)' : 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(8px)',
          borderBottom: 1,
          borderColor: 'divider'
        }}
      >
        <Container maxWidth="lg">
          <Toolbar disableGutters>
            <Typography
              variant="h6"
              component="div"
              sx={{
                flexGrow: 1,
                fontWeight: 700,
                color: isDarkMode ? '#fff' : '#000',
                cursor: 'pointer'
              }}
              onClick={() => handleNavigate('/')}
            >
              趣词
            </Typography>

            {/* 桌面端导航 */}
            {!isMobile && (
              <Stack direction="row" spacing={2} alignItems="center">
                {menuItems.map((item) => (
                  <Button
                    key={item.path}
                    onClick={() => handleNavigate(item.path)}
                    sx={{
                      color: location.pathname === item.path 
                        ? (isDarkMode ? muiTheme.palette.primary.light : muiTheme.palette.primary.main)
                        : (isDarkMode ? '#fff' : '#000'),
                      '&:hover': {
                        backgroundColor: isDarkMode 
                          ? 'rgba(255, 255, 255, 0.08)'
                          : 'rgba(0, 0, 0, 0.04)'
                      }
                    }}
                  >
                    {item.title}
                  </Button>
                ))}
                
                {/* 主题切换按钮 */}
                <IconButton 
                  onClick={toggleTheme}
                  sx={{ 
                    color: isDarkMode ? '#fff' : '#000',
                    ml: 1
                  }}
                >
                  {isDarkMode ? <Brightness7Icon /> : <Brightness4Icon />}
                </IconButton>

                <Button
                  variant="outlined"
                  startIcon={<PersonOutlineIcon />}
                  onClick={handleLoginClick}
                  sx={{
                    color: isDarkMode ? '#fff' : '#000',
                    borderColor: isDarkMode ? '#fff' : '#000',
                    '&:hover': {
                      borderColor: isDarkMode ? '#fff' : '#000',
                      backgroundColor: isDarkMode 
                        ? 'rgba(255, 255, 255, 0.08)'
                        : 'rgba(0, 0, 0, 0.04)'
                    }
                  }}
                >
                  登录
                </Button>
              </Stack>
            )}

            {/* 移动端菜单按钮 */}
            {isMobile && (
              <>
                <IconButton
                  onClick={toggleTheme}
                  sx={{ 
                    color: isDarkMode ? '#fff' : '#000',
                    mr: 1
                  }}
                >
                  {isDarkMode ? <Brightness7Icon /> : <Brightness4Icon />}
                </IconButton>
                <IconButton
                  edge="end"
                  onClick={handleDrawerToggle}
                  sx={{ color: isDarkMode ? '#fff' : '#000' }}
                >
                  {mobileOpen ? <CloseIcon /> : <MenuIcon />}
                </IconButton>
              </>
            )}
          </Toolbar>
        </Container>
      </AppBar>

      {/* 移动端抽屉菜单 */}
      <Drawer
        anchor="right"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        sx={{
          '& .MuiDrawer-paper': {
            width: 240,
            backgroundColor: isDarkMode ? '#121212' : '#fff',
          },
        }}
      >
        <List>
          {menuItems.map((item) => (
            <ListItem key={item.path} disablePadding>
              <ListItemButton
                onClick={() => handleNavigate(item.path)}
                selected={location.pathname === item.path}
                sx={{
                  color: isDarkMode ? '#fff' : '#000',
                  '&.Mui-selected': {
                    backgroundColor: isDarkMode 
                      ? 'rgba(255, 255, 255, 0.08)'
                      : 'rgba(0, 0, 0, 0.04)',
                  },
                  '&:hover': {
                    backgroundColor: isDarkMode 
                      ? 'rgba(255, 255, 255, 0.08)'
                      : 'rgba(0, 0, 0, 0.04)',
                  }
                }}
              >
                <ListItemText primary={item.title} />
              </ListItemButton>
            </ListItem>
          ))}
          <Divider sx={{ my: 1 }} />
          <ListItem disablePadding>
            <ListItemButton
              onClick={handleLoginClick}
              sx={{
                color: isDarkMode ? '#fff' : '#000',
              }}
            >
              <ListItemText primary="登录" />
            </ListItemButton>
          </ListItem>
        </List>
      </Drawer>

      <LoginDialog
        open={loginOpen}
        onClose={handleLoginClose}
        onRegister={handleRegister}
      />
    </>
  );
};

export default Navigation; 