import { Grid, Card, CardContent, CardMedia, Typography, Box } from '@mui/material';
import SchoolIcon from '@mui/icons-material/School';
import WorkIcon from '@mui/icons-material/Work';
import PublicIcon from '@mui/icons-material/Public';

interface WordBank {
  id: string;
  name: string;
  description: string;
  icon: 'school' | 'work' | 'public';
  totalWords: number;
}

const wordBanks: WordBank[] = [
  {
    id: 'cet4',
    name: 'CET-4 词汇',
    description: '大学英语四级考试核心词汇',
    icon: 'school',
    totalWords: 2500
  },
  {
    id: 'cet6',
    name: 'CET-6 词汇',
    description: '大学英语六级考试重点词汇',
    icon: 'school',
    totalWords: 2000
  },
  {
    id: 'gaokao',
    name: '高考词汇',
    description: '高考英语必备词汇',
    icon: 'school',
    totalWords: 3500
  }
];

interface WordBankSelectorProps {
  onSelect: (bankId: string) => void;
}

const getIcon = (iconName: WordBank['icon']) => {
  switch (iconName) {
    case 'school':
      return <SchoolIcon sx={{ fontSize: 40 }} />;
    case 'work':
      return <WorkIcon sx={{ fontSize: 40 }} />;
    case 'public':
      return <PublicIcon sx={{ fontSize: 40 }} />;
  }
};

export const WordBankSelector = ({ onSelect }: WordBankSelectorProps) => {
  return (
    <Grid container spacing={3} sx={{ mt: 2 }}>
      {wordBanks.map((bank) => (
        <Grid item xs={12} sm={6} md={4} key={bank.id}>
          <Card 
            sx={{ 
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              cursor: 'pointer',
              transition: 'transform 0.2s, box-shadow 0.2s',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: (theme) => theme.shadows[4],
              }
            }}
            onClick={() => onSelect(bank.id)}
          >
            <CardContent sx={{ flexGrow: 1 }}>
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                mb: 2,
                color: 'primary.main'
              }}>
                {getIcon(bank.icon)}
              </Box>
              <Typography variant="h5" component="h2" gutterBottom>
                {bank.name}
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                {bank.description}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                包含 {bank.totalWords} 个单词
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
}; 