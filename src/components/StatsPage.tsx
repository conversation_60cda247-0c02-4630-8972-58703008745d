import React from 'react';
import { useNavigate } from 'react-router-dom';
import './StatsPage.css';

interface StatsData {
  totalWords: number;
  masteredWords: number;
  learningWords: number;
  reviewWords: number;
  dailyStreak: number;
  lastWeekProgress: {
    date: string;
    wordsLearned: number;
    wordsReviewed: number;
  }[];
}

const StatsPage: React.FC = () => {
  const navigate = useNavigate();
  
  // 示例统计数据
  const statsData: StatsData = {
    totalWords: 150,
    masteredWords: 75,
    learningWords: 45,
    reviewWords: 30,
    dailyStreak: 7,
    lastWeekProgress: [
      { date: '2024-03-14', wordsLearned: 10, wordsReviewed: 20 },
      { date: '2024-03-15', wordsLearned: 8, wordsReviewed: 15 },
      { date: '2024-03-16', wordsLearned: 12, wordsReviewed: 25 },
      { date: '2024-03-17', wordsLearned: 15, wordsReviewed: 30 },
      { date: '2024-03-18', wordsLearned: 9, wordsReviewed: 18 },
      { date: '2024-03-19', wordsLearned: 11, wordsReviewed: 22 },
      { date: '2024-03-20', wordsLearned: 13, wordsReviewed: 28 },
    ]
  };

  const calculateProgress = (current: number, total: number) => {
    return (current / total) * 100;
  };

  return (
    <div className="stats-page">
      <nav className="navbar">
        <button className="back-button" onClick={() => navigate('/')}>返回</button>
        <h1>学习统计</h1>
      </nav>

      <main className="stats-content">
        <section className="overview-cards">
          <div className="stat-card">
            <h3>总单词量</h3>
            <div className="stat-value">{statsData.totalWords}</div>
          </div>
          <div className="stat-card">
            <h3>已掌握</h3>
            <div className="stat-value">{statsData.masteredWords}</div>
            <div className="progress-bar">
              <div 
                className="progress-fill"
                style={{ width: `${calculateProgress(statsData.masteredWords, statsData.totalWords)}%` }}
              ></div>
            </div>
          </div>
          <div className="stat-card">
            <h3>学习中</h3>
            <div className="stat-value">{statsData.learningWords}</div>
            <div className="progress-bar">
              <div 
                className="progress-fill"
                style={{ width: `${calculateProgress(statsData.learningWords, statsData.totalWords)}%` }}
              ></div>
            </div>
          </div>
          <div className="stat-card">
            <h3>待复习</h3>
            <div className="stat-value">{statsData.reviewWords}</div>
            <div className="progress-bar">
              <div 
                className="progress-fill"
                style={{ width: `${calculateProgress(statsData.reviewWords, statsData.totalWords)}%` }}
              ></div>
            </div>
          </div>
        </section>

        <section className="streak-section">
          <h2>连续学习</h2>
          <div className="streak-value">{statsData.dailyStreak} 天</div>
        </section>

        <section className="weekly-progress">
          <h2>最近一周学习情况</h2>
          <div className="progress-chart">
            {statsData.lastWeekProgress.map((day, index) => (
              <div key={index} className="chart-bar">
                <div className="bar-label">{day.date.split('-')[2]}</div>
                <div className="bar-container">
                  <div 
                    className="bar-learned"
                    style={{ height: `${(day.wordsLearned / 15) * 100}%` }}
                  ></div>
                  <div 
                    className="bar-reviewed"
                    style={{ height: `${(day.wordsReviewed / 30) * 100}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
          <div className="chart-legend">
            <div className="legend-item">
              <div className="legend-color learned"></div>
              <span>新学单词</span>
            </div>
            <div className="legend-item">
              <div className="legend-color reviewed"></div>
              <span>复习单词</span>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
};

export default StatsPage; 