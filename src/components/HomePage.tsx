import React from 'react';
import { Link } from 'react-router-dom';
import './HomePage.css';

const HomePage: React.FC = () => {
  return (
    <div className="home-page">
      <nav className="navbar">
        <div className="logo">
          <h1>LinkWord</h1>
        </div>
        <div className="nav-links">
          <Link to="/">首页</Link>
          <Link to="/learn">学习</Link>
          <Link to="/review">复习</Link>
          <Link to="/stats">统计</Link>
        </div>
      </nav>

      <main className="main-content">
        <section className="hero">
          <h2>提升你的词汇量</h2>
          <p>通过科学的学习方法，轻松掌握新单词</p>
          <Link to="/learn" className="cta-button">开始学习</Link>
        </section>

        <section className="features">
          <div className="feature-card">
            <h3>智能学习</h3>
            <p>根据记忆曲线，智能安排复习时间</p>
          </div>
          <div className="feature-card">
            <h3>进度追踪</h3>
            <p>实时查看学习进度和掌握情况</p>
          </div>
          <div className="feature-card">
            <h3>数据分析</h3>
            <p>详细的学习数据分析和统计</p>
          </div>
        </section>
      </main>
    </div>
  );
};

export default HomePage; 