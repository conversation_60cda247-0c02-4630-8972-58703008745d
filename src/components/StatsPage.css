.stats-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 2rem;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.back-button {
  padding: 0.5rem 1rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.back-button:hover {
  background-color: #0056b3;
}

.stats-content {
  max-width: 1200px;
  margin: 0 auto;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.stat-card {
  background-color: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-card h3 {
  color: #666;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.stat-value {
  font-size: 2.5rem;
  color: #333;
  font-weight: bold;
  margin-bottom: 1rem;
}

.progress-bar {
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #007bff;
  transition: width 0.3s ease;
}

.streak-section {
  background-color: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  margin-bottom: 3rem;
}

.streak-section h2 {
  color: #333;
  margin: 0 0 1rem 0;
}

.streak-value {
  font-size: 3rem;
  color: #007bff;
  font-weight: bold;
}

.weekly-progress {
  background-color: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.weekly-progress h2 {
  color: #333;
  margin: 0 0 2rem 0;
}

.progress-chart {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 300px;
  margin-bottom: 1rem;
}

.chart-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.bar-label {
  color: #666;
  font-size: 0.9rem;
}

.bar-container {
  width: 30px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  gap: 2px;
}

.bar-learned {
  background-color: #007bff;
  border-radius: 2px 2px 0 0;
  transition: height 0.3s ease;
}

.bar-reviewed {
  background-color: #28a745;
  border-radius: 2px 2px 0 0;
  transition: height 0.3s ease;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.legend-color.learned {
  background-color: #007bff;
}

.legend-color.reviewed {
  background-color: #28a745;
}

@media (max-width: 768px) {
  .stats-page {
    padding: 1rem;
  }

  .overview-cards {
    grid-template-columns: 1fr;
  }

  .progress-chart {
    height: 200px;
  }

  .bar-container {
    width: 20px;
  }
} 