import React from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Stack,
  Avatar,
  LinearProgress,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  IconButton,
  Button
} from '@mui/material';
import {
  Timeline,
  CheckCircle,
  TrendingUp,
  EmojiEvents,
  Settings,
  Notifications,
  VolumeUp,
  Brightness4,
  Security,
  Edit
} from '@mui/icons-material';

// 模拟的用户数据
const userData = {
  name: '张三',
  email: '<EMAIL>',
  avatar: 'https://source.unsplash.com/random/100x100?face',
  membershipType: '季度会员',
  expiryDate: '2024-06-30',
  stats: {
    wordsLearned: 1250,
    totalWords: 2000,
    daysStreak: 7,
    accuracy: 92,
    averageWPM: 45
  },
  recentActivity: [
    { date: '2024-03-15', words: 50, time: 25 },
    { date: '2024-03-14', words: 45, time: 20 },
    { date: '2024-03-13', words: 60, time: 30 },
    { date: '2024-03-12', words: 40, time: 15 },
    { date: '2024-03-11', words: 55, time: 28 }
  ]
};

export const UserProfile = () => {
  return (
    <Box sx={{ 
      minHeight: '100vh',
      bgcolor: '#1a1b26',
      color: 'white',
      pt: 8
    }}>
      <Container maxWidth="lg">
        <Box sx={{ 
          display: 'flex', 
          flexWrap: 'wrap',
          gap: 3
        }}>
          {/* 左侧个人信息 */}
          <Box sx={{ 
            flex: {
              xs: '1 1 100%',
              md: '1 1 calc(33.333% - 16px)'
            }
          }}>
            <Card sx={{ bgcolor: '#1f2937', mb: 3 }}>
              <CardContent>
                <Stack alignItems="center" spacing={2}>
                  <Box sx={{ position: 'relative' }}>
                    <Avatar
                      src={userData.avatar}
                      sx={{ width: 100, height: 100 }}
                    />
                    <IconButton
                      size="small"
                      sx={{
                        position: 'absolute',
                        right: -8,
                        bottom: -8,
                        bgcolor: '#60a5fa',
                        '&:hover': { bgcolor: '#3b82f6' }
                      }}
                    >
                      <Edit sx={{ fontSize: 16 }} />
                    </IconButton>
                  </Box>
                  <Typography variant="h6">
                    {userData.name}
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.7 }}>
                    {userData.email}
                  </Typography>
                  <Box sx={{ width: '100%' }}>
                    <Stack direction="row" justifyContent="space-between" sx={{ mb: 1 }}>
                      <Typography variant="body2" sx={{ opacity: 0.7 }}>
                        会员状态
                      </Typography>
                      <Typography variant="body2" color="#60a5fa">
                        {userData.membershipType}
                      </Typography>
                    </Stack>
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="body2" sx={{ opacity: 0.7 }}>
                        到期时间
                      </Typography>
                      <Typography variant="body2" color="#60a5fa">
                        {userData.expiryDate}
                      </Typography>
                    </Stack>
                  </Box>
                  <Button
                    fullWidth
                    variant="contained"
                    sx={{
                      bgcolor: '#60a5fa',
                      '&:hover': { bgcolor: '#3b82f6' }
                    }}
                  >
                    续费会员
                  </Button>
                </Stack>
              </CardContent>
            </Card>

            <Card sx={{ bgcolor: '#1f2937' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  设置
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <Notifications sx={{ color: 'rgba(255,255,255,0.7)' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="学习提醒"
                      secondary="每日提醒我学习"
                      sx={{
                        '& .MuiListItemText-secondary': {
                          color: 'rgba(255,255,255,0.5)'
                        }
                      }}
                    />
                    <ListItemSecondaryAction>
                      <Switch 
                        defaultChecked 
                        sx={{
                          '& .MuiSwitch-switchBase.Mui-checked': {
                            color: '#60a5fa'
                          },
                          '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                            backgroundColor: '#60a5fa'
                          }
                        }}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <VolumeUp sx={{ color: 'rgba(255,255,255,0.7)' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="自动发音"
                      secondary="切换单词时自动播放"
                      sx={{
                        '& .MuiListItemText-secondary': {
                          color: 'rgba(255,255,255,0.5)'
                        }
                      }}
                    />
                    <ListItemSecondaryAction>
                      <Switch 
                        defaultChecked 
                        sx={{
                          '& .MuiSwitch-switchBase.Mui-checked': {
                            color: '#60a5fa'
                          },
                          '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                            backgroundColor: '#60a5fa'
                          }
                        }}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <Brightness4 sx={{ color: 'rgba(255,255,255,0.7)' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="深色模式"
                      secondary="使用深色主题"
                      sx={{
                        '& .MuiListItemText-secondary': {
                          color: 'rgba(255,255,255,0.5)'
                        }
                      }}
                    />
                    <ListItemSecondaryAction>
                      <Switch 
                        defaultChecked 
                        sx={{
                          '& .MuiSwitch-switchBase.Mui-checked': {
                            color: '#60a5fa'
                          },
                          '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                            backgroundColor: '#60a5fa'
                          }
                        }}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Box>

          {/* 右侧学习统计 */}
          <Box sx={{ 
            flex: {
              xs: '1 1 100%',
              md: '1 1 calc(66.666% - 16px)'
            }
          }}>
            <Card sx={{ bgcolor: '#1f2937', mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  学习进度
                </Typography>
                <Box sx={{ mb: 3 }}>
                  <Stack direction="row" justifyContent="space-between" sx={{ mb: 1 }}>
                    <Typography variant="body2" sx={{ opacity: 0.7 }}>
                      已学习单词
                    </Typography>
                    <Typography variant="body2">
                      {userData.stats.wordsLearned} / {userData.stats.totalWords}
                    </Typography>
                  </Stack>
                  <LinearProgress 
                    variant="determinate" 
                    value={(userData.stats.wordsLearned / userData.stats.totalWords) * 100}
                    sx={{
                      bgcolor: 'rgba(255,255,255,0.1)',
                      '& .MuiLinearProgress-bar': {
                        bgcolor: '#60a5fa'
                      }
                    }}
                  />
                </Box>
                <Box sx={{ 
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: 3
                }}>
                  <Box sx={{ 
                    flex: {
                      xs: '1 1 calc(50% - 12px)',
                      md: '1 1 calc(25% - 18px)'
                    }
                  }}>
                    <Stack alignItems="center" spacing={1}>
                      <Timeline sx={{ color: '#60a5fa', fontSize: 40 }} />
                      <Typography variant="h4">
                        {userData.stats.daysStreak}
                      </Typography>
                      <Typography variant="body2" sx={{ opacity: 0.7 }}>
                        连续学习天数
                      </Typography>
                    </Stack>
                  </Box>
                  <Box sx={{ 
                    flex: {
                      xs: '1 1 calc(50% - 12px)',
                      md: '1 1 calc(25% - 18px)'
                    }
                  }}>
                    <Stack alignItems="center" spacing={1}>
                      <CheckCircle sx={{ color: '#60a5fa', fontSize: 40 }} />
                      <Typography variant="h4">
                        {userData.stats.accuracy}%
                      </Typography>
                      <Typography variant="body2" sx={{ opacity: 0.7 }}>
                        正确率
                      </Typography>
                    </Stack>
                  </Box>
                  <Box sx={{ 
                    flex: {
                      xs: '1 1 calc(50% - 12px)',
                      md: '1 1 calc(25% - 18px)'
                    }
                  }}>
                    <Stack alignItems="center" spacing={1}>
                      <TrendingUp sx={{ color: '#60a5fa', fontSize: 40 }} />
                      <Typography variant="h4">
                        {userData.stats.averageWPM}
                      </Typography>
                      <Typography variant="body2" sx={{ opacity: 0.7 }}>
                        平均WPM
                      </Typography>
                    </Stack>
                  </Box>
                  <Box sx={{ 
                    flex: {
                      xs: '1 1 calc(50% - 12px)',
                      md: '1 1 calc(25% - 18px)'
                    }
                  }}>
                    <Stack alignItems="center" spacing={1}>
                      <EmojiEvents sx={{ color: '#60a5fa', fontSize: 40 }} />
                      <Typography variant="h4">
                        {userData.stats.wordsLearned}
                      </Typography>
                      <Typography variant="body2" sx={{ opacity: 0.7 }}>
                        掌握单词数
                      </Typography>
                    </Stack>
                  </Box>
                </Box>
              </CardContent>
            </Card>

            <Card sx={{ bgcolor: '#1f2937' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  最近活动
                </Typography>
                <Stack spacing={2}>
                  {userData.recentActivity.map((activity, index) => (
                    <Box key={index}>
                      <Stack 
                        direction="row" 
                        justifyContent="space-between"
                        alignItems="center"
                      >
                        <Stack direction="row" spacing={2} alignItems="center">
                          <Typography variant="body2" sx={{ opacity: 0.7 }}>
                            {activity.date}
                          </Typography>
                          <Typography>
                            学习了 {activity.words} 个单词
                          </Typography>
                        </Stack>
                        <Typography variant="body2" sx={{ opacity: 0.7 }}>
                          用时 {activity.time} 分钟
                        </Typography>
                      </Stack>
                      {index < userData.recentActivity.length - 1 && (
                        <Divider sx={{ mt: 2, bgcolor: 'rgba(255,255,255,0.1)' }} />
                      )}
                    </Box>
                  ))}
                </Stack>
              </CardContent>
            </Card>
          </Box>
        </Box>
      </Container>
    </Box>
  );
}; 