import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Card, 
  Typography, 
  Button, 
  IconButton,
  Container,
  Paper,
  Grid as MuiGrid
} from '@mui/material';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import './ReviewPage.css';

interface Word {
  id: number;
  word: string;
  phonetic: string;
  definition: string;
  examples: string[];
  etymology: string;
}

const ReviewPage: React.FC = () => {
  const [currentWord, setCurrentWord] = useState<Word | null>(null);
  const [showAnswer, setShowAnswer] = useState(false);
  const [audio, setAudio] = useState<HTMLAudioElement | null>(null);

  useEffect(() => {
    // 模拟获取单词数据
    const mockWord: Word = {
      id: 1,
      word: "dormitory",
      phonetic: "/'dɔːmɪt(ə)rɪ/",
      definition: "宿舍，学生宿舍",
      examples: [
        "She lived in a college dormitory.",
        "The students have barricaded themselves into their dormitory building."
      ],
      etymology: "来自拉丁语dormitorium(睡觉的地方),源自dormire(睡觉)"
    };
    setCurrentWord(mockWord);
    
    // 初始化音频
    const audio = new Audio(`https://api.dictionaryapi.dev/media/pronunciations/en/${mockWord.word}.mp3`);
    setAudio(audio);
  }, []);

  const playSound = () => {
    if (audio) {
      audio.play().catch(error => {
        console.error('播放失败:', error);
      });
    }
  };

  const handleNextWord = () => {
    setShowAnswer(false);
    // TODO: 获取下一个单词
  };

  if (!currentWord) return <div>Loading...</div>;

  return (
    <Container maxWidth="md" className="review-container">
      <Paper elevation={3} className="review-card">
        <MuiGrid container spacing={3}>
          <MuiGrid component="div" item xs={12}>
            <Box className="word-header">
              <Typography variant="h3" className="word-text">
                {currentWord.word}
              </Typography>
              <IconButton onClick={playSound} color="primary">
                <VolumeUpIcon />
              </IconButton>
            </Box>
            <Typography variant="subtitle1" color="textSecondary">
              {currentWord.phonetic}
            </Typography>
          </MuiGrid>

          {showAnswer ? (
            <>
              <MuiGrid component="div" item xs={12}>
                <Typography variant="h6">释义</Typography>
                <Typography>{currentWord.definition}</Typography>
              </MuiGrid>
              <MuiGrid component="div" item xs={12}>
                <Typography variant="h6">例句</Typography>
                {currentWord.examples.map((example, index) => (
                  <Typography key={index} className="example-text">
                    {example}
                  </Typography>
                ))}
              </MuiGrid>
              <MuiGrid component="div" item xs={12}>
                <Typography variant="h6">词源</Typography>
                <Typography>{currentWord.etymology}</Typography>
              </MuiGrid>
            </>
          ) : null}

          <MuiGrid component="div" item xs={12} className="button-group">
            <Button 
              variant="contained" 
              color="primary" 
              onClick={() => setShowAnswer(true)}
              disabled={showAnswer}
            >
              显示答案
            </Button>
            <Button 
              variant="outlined" 
              color="primary" 
              onClick={handleNextWord}
            >
              下一个
            </Button>
          </MuiGrid>
        </MuiGrid>
      </Paper>
    </Container>
  );
};

export default ReviewPage; 