import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import './LearnPage.css';

interface Word {
  id: number;
  word: string;
  pronunciation: string;
  meaning: string;
  example: string;
}

const LearnPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentWordIndex, setCurrentWordIndex] = useState(0);
  const [showMeaning, setShowMeaning] = useState(false);
  
  // 示例单词数据
  const words: Word[] = [
    {
      id: 1,
      word: "perseverance",
      pronunciation: "/ˌpɜːsɪˈvɪərəns/",
      meaning: "毅力，坚持不懈",
      example: "Success comes to those who have perseverance."
    },
    {
      id: 2,
      word: "eloquent",
      pronunciation: "/ˈeləkwənt/",
      meaning: "雄辩的，有说服力的",
      example: "She gave an eloquent speech at the conference."
    }
  ];

  const currentWord = words[currentWordIndex];

  const handleNext = () => {
    if (currentWordIndex < words.length - 1) {
      setCurrentWordIndex(currentWordIndex + 1);
      setShowMeaning(false);
    }
  };

  const handlePrevious = () => {
    if (currentWordIndex > 0) {
      setCurrentWordIndex(currentWordIndex - 1);
      setShowMeaning(false);
    }
  };

  const handleSpeak = () => {
    const utterance = new SpeechSynthesisUtterance(currentWord.word);
    utterance.lang = 'en-US';
    window.speechSynthesis.speak(utterance);
  };

  return (
    <div className="learn-page">
      <nav className="navbar">
        <button className="back-button" onClick={() => navigate('/')}>返回</button>
        <h1>单词学习</h1>
        <div className="progress">
          {currentWordIndex + 1} / {words.length}
        </div>
      </nav>

      <main className="word-card">
        <div className="word-header">
          <h2>{currentWord.word}</h2>
          <button className="speak-button" onClick={handleSpeak}>
            🔊
          </button>
        </div>
        
        <div className="pronunciation">{currentWord.pronunciation}</div>
        
        <div className="meaning-section">
          <button 
            className="show-meaning-button"
            onClick={() => setShowMeaning(!showMeaning)}
          >
            {showMeaning ? '隐藏释义' : '显示释义'}
          </button>
          
          {showMeaning && (
            <div className="meaning-content">
              <p className="meaning">{currentWord.meaning}</p>
              <p className="example">{currentWord.example}</p>
            </div>
          )}
        </div>

        <div className="navigation-buttons">
          <button 
            className="nav-button"
            onClick={handlePrevious}
            disabled={currentWordIndex === 0}
          >
            上一个
          </button>
          <button 
            className="nav-button"
            onClick={handleNext}
            disabled={currentWordIndex === words.length - 1}
          >
            下一个
          </button>
        </div>
      </main>
    </div>
  );
};

export default LearnPage; 