import type { WordBank } from '../types/word';

export const wordBanks: WordBank[] = [
  {
    id: 1,
    name: 'CET-4 核心词汇',
    description: '大学英语四级考试必备词汇',
    totalWords: 3,
    words: [
      {
        id: 1,
        word: 'dormitory',
        phonetic: "/'dɔːmɪt(ə)rɪ/",
        definition: '宿舍，学生宿舍',
        examples: [
          'She lived in a college dormitory.',
          'The students have barricaded themselves into their dormitory building.'
        ],
        etymology: '来自拉丁语dormitorium(睡觉的地方),源自dormire(睡觉)',
        rootsAndAffixes: [
          {
            root: 'dorm',
            meaning: '睡眠',
            relatedWords: ['dormant', 'dormer', 'dormitive']
          }
        ],
        difficulty: 3,
        tags: ['CET4', 'BUILDING', 'SCHOOL']
      },
      {
        id: 2,
        word: 'discrepancy',
        phonetic: "/dɪs'krep(ə)nsɪ/",
        definition: '差异；不一致；矛盾',
        examples: [
          'There is a discrepancy between the two accounts.',
          'We noticed some discrepancies in the financial reports.'
        ],
        etymology: '来自拉丁语discrepantia，源自dis-(不同) + crepare(发出声音)',
        rootsAndAffixes: [
          {
            root: 'dis',
            meaning: '不、相反',
            relatedWords: ['disagree', 'disappear', 'disorder']
          }
        ],
        difficulty: 4,
        tags: ['CET4', 'ACADEMIC']
      },
      {
        id: 3,
        word: 'incumbent',
        phonetic: "/ɪn'kʌmbənt/",
        definition: '在职的；现任的；负有职责的',
        examples: [
          'The incumbent president is running for re-election.',
          'It is incumbent upon us to help those in need.'
        ],
        etymology: '来自拉丁语incumbere，意为"倚靠；依赖"，由in-(在上) + -cumbere(躺)',
        rootsAndAffixes: [
          {
            root: 'cumb',
            meaning: '躺',
            relatedWords: ['succumb', 'recumbent']
          }
        ],
        difficulty: 4,
        tags: ['CET6', 'POLITICS', 'FORMAL']
      }
    ],
    createdAt: '2024-03-20T00:00:00Z',
    updatedAt: '2024-03-20T00:00:00Z'
  }
]; 