{"name": "link_word", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.2.5", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.1", "@mui/system": "^7.1.0", "@types/react-window": "^1.8.8", "@types/react-window-infinite-loader": "^1.0.9", "axios": "^1.9.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "use-sound": "^5.0.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^22.15.30", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "uuid": "^11.1.0", "vite": "^6.3.5"}}